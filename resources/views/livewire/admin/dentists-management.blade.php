<div class="p-6 space-y-6">
    <!-- <PERSON><PERSON><PERSON> Header Section -->
    <div wire:ignore class="bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-slate-800 dark:to-slate-700 rounded-xl p-6 border border-indigo-100 dark:border-slate-600">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                    <div class="h-12 w-12 bg-indigo-500 rounded-xl flex items-center justify-center">
                        <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-slate-900 dark:text-white">Dentists Management</h1>
                    <p class="mt-1 text-sm text-slate-600 dark:text-slate-400">
                        Manage dental office accounts, track their status, and maintain professional relationships
                    </p>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="flex items-center space-x-6 text-sm">
                <div class="text-center">
                    <div class="text-lg font-semibold text-indigo-600 dark:text-indigo-400">{{ $totalDentists }}</div>
                    <div class="text-slate-500 dark:text-slate-400">Total</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-semibold text-green-600 dark:text-green-400">{{ $activeDentists }}</div>
                    <div class="text-slate-500 dark:text-slate-400">Active</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Search and Actions Bar -->
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-4">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div class="flex flex-col sm:flex-row gap-4 flex-1">
                <!-- Enhanced Search Input -->
                <div class="relative flex-1 max-w-md">
                    <flux:input
                        wire:model.live.debounce.300ms="search"
                        placeholder="Search by name, email, or ID..."
                        class="w-full"
                        icon="magnifying-glass"
                    />
                    @if($search)
                        <button
                            wire:click="$set('search', '')"
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                        >
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    @endif
                </div>

                <!-- Enhanced Status Filter -->
                <div class="min-w-[140px]">
                    <flux:select wire:model.live="statusFilter" placeholder="All Status">
                        <flux:select.option value="">All Status</flux:select.option>
                        <flux:select.option value="1">✅ Active</flux:select.option>
                        <flux:select.option value="0">❌ Inactive</flux:select.option>
                    </flux:select>
                </div>
            </div>

            <!-- Enhanced Add Dentist Button -->
            <div class="flex items-center gap-3">
                <flux:tooltip content="Add a new dentist to the system">
                    <flux:button wire:click="openCreateModal" variant="primary" icon="plus" class="shadow-sm">
                        Add Dentist
                    </flux:button>
                </flux:tooltip>
            </div>
        </div>
    </div>

    <!-- Enhanced Dentists Table -->
    <div class="bg-white dark:bg-slate-800 shadow-sm rounded-lg overflow-hidden border border-slate-200 dark:border-slate-700">
        <!-- Table Header with Count -->
        <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700/50">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <h3 class="text-lg font-medium text-slate-900 dark:text-white">Dentists Directory</h3>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                        {{ $dentists->total() }} {{ Str::plural('dentist', $dentists->total()) }}
                    </span>
                </div>
                @if($search || $statusFilter !== '')
                    <button
                        wire:click="clearFilters"
                        class="text-sm text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 flex items-center space-x-1"
                    >
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        <span>Clear filters</span>
                    </button>
                @endif
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                <thead class="bg-slate-50 dark:bg-slate-700">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            <div class="flex items-center space-x-1">
                                <span>Dentist</span>
                                <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            <div class="flex items-center space-x-1">
                                <span>Contact</span>
                                <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            <div class="flex items-center space-x-1">
                                <span>Status</span>
                                <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            <div class="flex items-center space-x-1">
                                <span>Email Verification</span>
                                <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            <div class="flex items-center space-x-1">
                                <span>Joined</span>
                                <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </th>
                        <th class="px-6 py-4 text-right text-xs font-semibold text-slate-600 dark:text-slate-300 uppercase tracking-wider whitespace-nowrap">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-700">
                    @forelse ($dentists as $dentist)
                        <tr class="hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-11 w-11">
                                        <div class="h-11 w-11 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-sm">
                                            <span class="text-sm font-semibold text-white">{{ $dentist->initials() }}</span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-semibold text-slate-900 dark:text-white">
                                            {{ $dentist->name }}
                                        </div>
                                        <div class="text-xs text-slate-500 dark:text-slate-400 flex items-center space-x-1">
                                            <span>ID: #{{ $dentist->id }}</span>
                                            <span class="w-1 h-1 bg-slate-300 rounded-full"></span>
                                            <span>{{ $dentist->created_at->diffForHumans() }}</span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="space-y-1">
                                    <div class="text-sm font-medium text-slate-900 dark:text-white flex items-center space-x-2">
                                        <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        <span>{{ $dentist->email }}</span>
                                    </div>
                                    @if ($dentist->phone)
                                        <div class="text-sm text-slate-500 dark:text-slate-400 flex items-center space-x-2">
                                            <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                            <span>{{ $dentist->phone }}</span>
                                        </div>
                                    @else
                                        <div class="text-xs text-slate-400 dark:text-slate-500 italic">No phone provided</div>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <flux:badge :color="$dentist->status_badge_color" size="sm" class="font-medium">
                                    {{ $dentist->status_text }}
                                </flux:badge>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <flux:badge :color="$dentist->email_verification_badge_color" size="sm" class="font-medium">
                                    {{ $dentist->email_verification_text }}
                                </flux:badge>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-slate-900 dark:text-white font-medium">
                                    {{ $dentist->created_at->format('M d, Y') }}
                                </div>
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    {{ $dentist->created_at->format('g:i A') }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-1">
                                    <!-- View Details -->
                                    <flux:tooltip content="View dentist details">
                                        <flux:button
                                            wire:click="openDetailsModal({{ $dentist->id }})"
                                            variant="ghost"
                                            size="sm"
                                            icon="eye"
                                            class="text-slate-500 hover:text-indigo-600 dark:text-slate-400 dark:hover:text-indigo-400 transition-colors duration-200">
                                        </flux:button>
                                    </flux:tooltip>

                                    <!-- Edit -->
                                    <flux:tooltip content="Edit dentist information">
                                        <flux:button
                                            wire:click="openEditModal({{ $dentist->id }})"
                                            variant="ghost"
                                            size="sm"
                                            icon="pencil"
                                            class="text-slate-500 hover:text-blue-600 dark:text-slate-400 dark:hover:text-blue-400 transition-colors duration-200">
                                        </flux:button>
                                    </flux:tooltip>

                                    <!-- Toggle Status -->
                                    <flux:tooltip content="{{ $dentist->is_active ? 'Deactivate dentist' : 'Activate dentist' }}">
                                        <flux:button
                                            wire:click="toggleStatus({{ $dentist->id }})"
                                            variant="ghost"
                                            size="sm"
                                            :icon="$dentist->is_active ? 'x-mark' : 'check'"
                                            :class="$dentist->is_active
                                                ? 'text-slate-500 hover:text-red-600 dark:text-slate-400 dark:hover:text-red-400'
                                                : 'text-slate-500 hover:text-green-600 dark:text-slate-400 dark:hover:text-green-400'"
                                            class="transition-colors duration-200">
                                        </flux:button>
                                    </flux:tooltip>

                                    <!-- Delete -->
                                    <flux:tooltip content="Delete dentist permanently">
                                        <flux:button
                                            wire:click="openDeleteModal({{ $dentist->id }})"
                                            variant="ghost"
                                            size="sm"
                                            icon="trash"
                                            class="text-slate-500 hover:text-red-600 dark:text-slate-400 dark:hover:text-red-400 transition-colors duration-200">
                                        </flux:button>
                                    </flux:tooltip>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-16 text-center">
                                <div class="flex flex-col items-center justify-center space-y-4">
                                    <div class="h-16 w-16 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                        <svg class="h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="text-center">
                                        <h3 class="text-lg font-medium text-slate-900 dark:text-white">
                                            @if ($search || $statusFilter !== '')
                                                No matching dentists found
                                            @else
                                                No dentists yet
                                            @endif
                                        </h3>
                                        <p class="mt-2 text-sm text-slate-500 dark:text-slate-400 max-w-sm">
                                            @if ($search || $statusFilter !== '')
                                                Try adjusting your search criteria or filters to find what you're looking for.
                                            @else
                                                Get started by adding your first dentist to the system. They'll be able to access their account and manage cases.
                                            @endif
                                        </p>
                                    </div>
                                    @if (!$search && $statusFilter === '')
                                        <flux:button wire:click="openCreateModal" variant="primary" icon="plus" class="mt-4">
                                            Add Your First Dentist
                                        </flux:button>
                                    @else
                                        <div class="flex items-center space-x-3 mt-4">
                                            <flux:button wire:click="clearFilters" variant="ghost" icon="x-mark">
                                                Clear Filters
                                            </flux:button>
                                            <flux:button wire:click="openCreateModal" variant="primary" icon="plus">
                                                Add Dentist
                                            </flux:button>
                                        </div>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Enhanced Pagination -->
        @if ($dentists->hasPages())
            <div class="px-6 py-4 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700/50">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-slate-500 dark:text-slate-400">
                        Showing {{ $dentists->firstItem() }} to {{ $dentists->lastItem() }} of {{ $dentists->total() }} results
                    </div>
                    <div>
                        {{ $dentists->links() }}
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Enhanced Form Modal (Add/Edit Dentist) -->
    <flux:modal wire:model="showFormModal" class="max-w-2xl w-full">
        <form wire:submit="save">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="h-10 w-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $dentistId ? 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z' : 'M12 6v6m0 0v6m0-6h6m-6 0H6' }}"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-slate-900 dark:text-white">
                            {{ $dentistId ? 'Edit Dentist Information' : 'Add New Dentist' }}
                        </h3>
                        <p class="text-sm text-slate-500 dark:text-slate-400">
                            {{ $dentistId ? 'Update the dentist\'s account details and settings' : 'Create a new dentist account with access credentials' }}
                        </p>
                    </div>
                </div>

                <div class="space-y-6">
                    <!-- Basic Information Section -->
                    <div class="bg-slate-50 dark:bg-slate-700/50 rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-slate-900 dark:text-white mb-4 flex items-center space-x-2">
                            <svg class="h-4 w-4 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span>Basic Information</span>
                        </h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Name -->
                            <div class="md:col-span-2">
                                <flux:field>
                                    <flux:label>Full Name *</flux:label>
                                    <flux:input wire:model="name" placeholder="Dr. John Smith" />
                                    <flux:error name="name" />
                                </flux:field>
                            </div>

                            <!-- Email -->
                            <div class="md:col-span-2">
                                <flux:field>
                                    <flux:label>Email Address *</flux:label>
                                    <flux:input wire:model="email" type="email" placeholder="<EMAIL>" />
                                    <flux:error name="email" />
                                </flux:field>
                            </div>

                            <!-- Phone -->
                            <div class="md:col-span-2">
                                <flux:field>
                                    <flux:label>Phone Number</flux:label>
                                    <flux:input wire:model="phone" placeholder="+**************** (optional)" />
                                    <flux:error name="phone" />
                                </flux:field>
                            </div>
                        </div>
                    </div>

                    <!-- Security Section -->
                    <div class="bg-slate-50 dark:bg-slate-700/50 rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-slate-900 dark:text-white mb-4 flex items-center space-x-2">
                            <svg class="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            <span>Security & Access</span>
                        </h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Password -->
                            <div>
                                <flux:field>
                                    <flux:label>
                                        Password {{ $dentistId ? '' : '*' }}
                                        @if($dentistId)
                                            <span class="text-xs text-slate-500">(leave blank to keep current)</span>
                                        @endif
                                    </flux:label>
                                    <flux:input wire:model="password" type="password" placeholder="Enter secure password" />
                                    <flux:error name="password" />
                                </flux:field>
                            </div>

                            <!-- Confirm Password -->
                            <div>
                                <flux:field>
                                    <flux:label>Confirm Password {{ $dentistId ? '' : '*' }}</flux:label>
                                    <flux:input wire:model="password_confirmation" type="password" placeholder="Confirm password" />
                                    <flux:error name="password_confirmation" />
                                </flux:field>
                            </div>
                        </div>
                    </div>

                    <!-- Account Settings Section -->
                    <div class="bg-slate-50 dark:bg-slate-700/50 rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-slate-900 dark:text-white mb-4 flex items-center space-x-2">
                            <svg class="h-4 w-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span>Account Settings</span>
                        </h4>

                        <div class="space-y-4">
                            <!-- Active Status -->
                            <div class="flex items-center justify-between p-3 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-600">
                                <div class="flex items-center space-x-3">
                                    <div class="h-8 w-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                                        <svg class="h-4 w-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-slate-900 dark:text-white">Active Status</div>
                                        <div class="text-xs text-slate-500 dark:text-slate-400">Allow the dentist to access their account</div>
                                    </div>
                                </div>
                                <flux:checkbox wire:model="is_active" />
                            </div>

                            <!-- Email Verification -->
                            <div class="flex items-center justify-between p-3 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-600">
                                <div class="flex items-center space-x-3">
                                    <div class="h-8 w-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                        <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-slate-900 dark:text-white">Email Verified</div>
                                        <div class="text-xs text-slate-500 dark:text-slate-400">Mark email as verified to skip verification process</div>
                                    </div>
                                </div>
                                <flux:checkbox wire:model="is_email_verified" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-between items-center p-6 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700/50">
                <div class="text-xs text-slate-500 dark:text-slate-400">
                    * Required fields
                </div>
                <div class="flex items-center gap-3">
                    <flux:button wire:click="closeFormModal" variant="ghost" class="px-4">
                        Cancel
                    </flux:button>
                    <flux:button type="submit" variant="primary" class="px-6 shadow-sm" icon="check">
                        {{ $dentistId ? 'Update Dentist' : 'Create Dentist' }}
                    </flux:button>
                </div>
            </div>
        </form>
    </flux:modal>

    <!-- Enhanced Confirmation Modal (Delete) -->
    <flux:modal wire:model="showConfirmModal" class="max-w-md">
        <div class="p-6">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="h-12 w-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white">
                        Delete Dentist Account
                    </h3>
                    <div class="mt-3 space-y-2">
                        <p class="text-sm text-slate-600 dark:text-slate-400">
                            You are about to permanently delete the account for:
                        </p>
                        @if($dentistToDelete)
                            <div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-3">
                                <div class="flex items-center space-x-3">
                                    <div class="h-8 w-8 bg-indigo-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-medium text-white">{{ $dentistToDelete->initials() }}</span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-slate-900 dark:text-white">{{ $dentistToDelete->name }}</div>
                                        <div class="text-xs text-slate-500 dark:text-slate-400">{{ $dentistToDelete->email }}</div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                            <div class="flex items-start space-x-2">
                                <svg class="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <div class="text-xs text-red-700 dark:text-red-300">
                                    <strong>Warning:</strong> This action cannot be undone. All associated data will be permanently removed.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex justify-end gap-3 p-6 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700/50">
            <flux:button wire:click="closeConfirmModal" variant="ghost" class="px-4">
                Cancel
            </flux:button>
            <flux:button wire:click="confirmDelete" variant="danger" class="px-6" icon="trash">
                Delete Permanently
            </flux:button>
        </div>
    </flux:modal>

    <!-- Enhanced Details Modal -->
    <flux:modal wire:model="showDetailsModal" class="max-w-3xl w-full">
        @if ($dentistDetails)
            <div class="p-6">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3">
                        <div class="h-10 w-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-slate-900 dark:text-white">
                                Dentist Profile Details
                            </h3>
                            <p class="text-sm text-slate-500 dark:text-slate-400">
                                Complete information and account status
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <flux:badge :color="$dentistDetails->status_badge_color" size="sm" class="font-medium">
                            {{ $dentistDetails->status_text }}
                        </flux:badge>
                        <flux:badge :color="$dentistDetails->email_verification_badge_color" size="sm" class="font-medium">
                            {{ $dentistDetails->email_verification_text }}
                        </flux:badge>
                    </div>
                </div>

                <div class="space-y-6">
                    <!-- Enhanced Profile Section -->
                    <div class="bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-slate-700 dark:to-slate-600 rounded-xl p-6 border border-indigo-100 dark:border-slate-500">
                        <div class="flex items-center space-x-6">
                            <div class="h-20 w-20 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                                <span class="text-2xl font-bold text-white">{{ $dentistDetails->initials() }}</span>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-2xl font-bold text-slate-900 dark:text-white">
                                    {{ $dentistDetails->name }}
                                </h4>
                                <div class="flex items-center space-x-4 mt-2 text-sm text-slate-600 dark:text-slate-300">
                                    <span class="flex items-center space-x-1">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                        </svg>
                                        <span>ID: #{{ $dentistDetails->id }}</span>
                                    </span>
                                    <span class="flex items-center space-x-1">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <span>Member since {{ $dentistDetails->created_at->format('M Y') }}</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Details Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Contact Information -->
                        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="h-8 w-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                                    <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <h5 class="font-semibold text-slate-900 dark:text-white">Email Address</h5>
                            </div>
                            <p class="text-sm text-slate-600 dark:text-slate-300 font-mono bg-slate-50 dark:bg-slate-700 px-3 py-2 rounded">
                                {{ $dentistDetails->email }}
                            </p>
                        </div>

                        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="h-8 w-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                                    <svg class="h-4 w-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <h5 class="font-semibold text-slate-900 dark:text-white">Phone Number</h5>
                            </div>
                            <p class="text-sm text-slate-600 dark:text-slate-300">
                                {{ $dentistDetails->phone ?: 'Not provided' }}
                            </p>
                        </div>

                        <!-- Account Status -->
                        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="h-8 w-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                                    <svg class="h-4 w-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <h5 class="font-semibold text-slate-900 dark:text-white">Account Status</h5>
                            </div>
                            <flux:badge :color="$dentistDetails->status_badge_color" size="sm" class="font-medium">
                                {{ $dentistDetails->status_text }}
                            </flux:badge>
                        </div>

                        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="h-8 w-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                                    <svg class="h-4 w-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                </div>
                                <h5 class="font-semibold text-slate-900 dark:text-white">Email Verification</h5>
                            </div>
                            <flux:badge :color="$dentistDetails->email_verification_badge_color" size="sm" class="font-medium">
                                {{ $dentistDetails->email_verification_text }}
                            </flux:badge>
                        </div>

                        <!-- Timeline Information -->
                        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="h-8 w-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center">
                                    <svg class="h-4 w-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <h5 class="font-semibold text-slate-900 dark:text-white">Account Created</h5>
                            </div>
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-900 dark:text-white">
                                    {{ $dentistDetails->created_at->format('F d, Y') }}
                                </p>
                                <p class="text-xs text-slate-500 dark:text-slate-400">
                                    {{ $dentistDetails->created_at->format('g:i A') }} • {{ $dentistDetails->created_at->diffForHumans() }}
                                </p>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-600">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="h-8 w-8 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center">
                                    <svg class="h-4 w-4 text-slate-600 dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </div>
                                <h5 class="font-semibold text-slate-900 dark:text-white">Last Updated</h5>
                            </div>
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-900 dark:text-white">
                                    {{ $dentistDetails->updated_at->format('F d, Y') }}
                                </p>
                                <p class="text-xs text-slate-500 dark:text-slate-400">
                                    {{ $dentistDetails->updated_at->format('g:i A') }} • {{ $dentistDetails->updated_at->diffForHumans() }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-between items-center p-6 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700/50">
                <div class="text-xs text-slate-500 dark:text-slate-400">
                    Account ID: #{{ $dentistDetails->id }}
                </div>
                <div class="flex items-center gap-3">
                    <flux:button wire:click="closeDetailsModal" variant="ghost" class="px-4">
                        Close
                    </flux:button>
                    <flux:button wire:click="openEditModal({{ $dentistDetails->id }})" variant="primary" class="px-6 shadow-sm" icon="pencil">
                        Edit Dentist
                    </flux:button>
                </div>
            </div>
        @endif
    </flux:modal>
</div>
